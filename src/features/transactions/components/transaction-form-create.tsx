import type { TransactionData } from "../types";

import { useMemo } from "react";
import { useForm } from "react-hook-form";

import { zodResolver } from "@hookform/resolvers/zod";
import { formatISO } from "date-fns";
import { LoaderIcon } from "lucide-react";
import { useShallow } from "zustand/react/shallow";

import ErrorMessage from "~/components/blocks/error-message";
import InputRadioGroup from "~/components/inputs/input-radio-group";
import InputSelect from "~/components/inputs/input-select";
import InputText from "~/components/inputs/input-text";
import InputTextarea from "~/components/inputs/input-textarea";
import { Button } from "~/components/ui/button";
import { Form } from "~/components/ui/form";
import { useAccounts } from "~/features/accounts/hooks";
import { useCategories } from "~/features/categories/hooks";

import { useTransactionCreate } from "../hooks";
import { TransactionRequestSchema } from "../schemas";
import useTransactionsStore from "../store";

interface Props {
  initialValues?: Partial<TransactionData>;
}

export default function TransactionFormCreate({ initialValues }: Props) {
  const setDialogOpen = useTransactionsStore(useShallow((state) => state.setDialogOpen));

  const { accounts } = useAccounts();
  const { categories } = useCategories();
  const { mutate: createTransaction, isPending, error } = useTransactionCreate();

  const accountOptions = useMemo(
    () => accounts.map((account) => ({ value: account.id, label: account.name })),
    [accounts]
  );

  const transactionTypeOptions = [
    { value: "income", label: "Income" },
    { value: "expense", label: "Expense" },
    { value: "transfer", label: "Transfer" },
  ];

  const defaultValues = useMemo<TransactionData>(
    () => ({
      transaction_type: initialValues?.transaction_type ?? "expense",
      transaction_date: initialValues?.transaction_date ?? formatISO(new Date(), { representation: "date" }),
      description: initialValues?.description ?? "",
      category_id: initialValues?.category_id ?? "",
      account_id: initialValues?.account_id ?? "",
      amount: initialValues?.amount ?? "0.00",
      account_to_id: initialValues?.account_to_id ?? "",
      amount_to: initialValues?.amount_to ?? "0.00",
    }),
    [initialValues]
  );

  const form = useForm<TransactionData>({
    defaultValues,
    resolver: zodResolver(TransactionRequestSchema),
  });

  const transactionType = form.watch("transaction_type");

  const categoryOptions = useMemo(
    () =>
      categories
        .filter((category) => (transactionType === "expense" ? category.is_expense : !category.is_expense))
        .map((category) => ({ value: category.id, label: category.name })),
    [categories, transactionType]
  );

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit((data) => createTransaction(data))} className="space-y-4">
        {error && <ErrorMessage title="Can't create transaction" error={error} />}

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <InputRadioGroup
            control={form.control}
            name="transaction_type"
            options={transactionTypeOptions}
            label="Transaction Type"
            className="md:col-span-2"
            disabled={isPending}
          />

          <InputText
            control={form.control}
            name="transaction_date"
            label="Date"
            type="date"
            disabled={isPending}
            required
          />

          <InputSelect
            control={form.control}
            name="account_id"
            values={accountOptions}
            label="Account"
            disabled={isPending}
            required
          />

          {transactionType === "transfer" && (
            <InputSelect
              control={form.control}
              name="account_to_id"
              values={accountOptions}
              label="To Account"
              disabled={isPending}
              required
            />
          )}

          <InputText
            control={form.control}
            name="amount"
            label="Amount"
            placeholder="0.00"
            type="number"
            min={0}
            step="0.01"
            disabled={isPending}
            required
          />

          {transactionType === "transfer" && (
            <InputText
              control={form.control}
              name="amount_to"
              label="To Amount"
              placeholder="0.00"
              type="number"
              min={0}
              step="0.01"
              disabled={isPending}
              required
            />
          )}

          {transactionType !== "transfer" && (
            <InputSelect
              control={form.control}
              name="category_id"
              values={categoryOptions}
              label="Category"
              disabled={isPending}
              clearable
            />
          )}

          <InputTextarea
            control={form.control}
            name="description"
            label="Description"
            placeholder="Transaction description"
            className="md:col-span-2"
            disabled={isPending}
          />
        </div>

        <div className="flex flex-col gap-2 sm:flex-row-reverse sm:justify-between">
          <Button type="submit" disabled={isPending}>
            {isPending ? <LoaderIcon className="animate-spin" /> : "Create transaction"}
          </Button>
          <Button type="button" variant="outline" disabled={isPending} onClick={() => setDialogOpen(false)}>
            Cancel
          </Button>
        </div>
      </form>
    </Form>
  );
}
