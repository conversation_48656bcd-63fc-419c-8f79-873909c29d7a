import type { ApiError } from "~/api/client";
import type { Transaction } from "../types";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useNavigate } from "@tanstack/react-router";
import { toast } from "sonner";
import { useShallow } from "zustand/react/shallow";

import { apiClient } from "~/api";

import useTransactionsStore from "../store";

export function useTransactionDelete() {
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const setDialogOpen = useTransactionsStore(useShallow((state) => state.setDialogOpen));

  return useMutation<Transaction, ApiError, string>({
    mutationFn: (id) => apiClient.delete(`/v1/transactions/${id}`),
    onSuccess: (transaction) => {
      void Promise.all([
        queryClient.invalidateQueries({ queryKey: ["transactions"] }),
        queryClient.invalidateQueries({ queryKey: ["accounts"] }),
        queryClient.invalidateQueries({ queryKey: ["stats"] }),
      ]);

      toast.success("Transaction deleted", {
        description: `Transaction for ${transaction.amount} deleted successfully.`,
      });

      setDialogOpen(false);

      return navigate({ to: "/transactions" });
    },
  });
}
